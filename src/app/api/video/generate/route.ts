import { NextRequest } from "next/server";
import { respData, respErr } from "@/lib/resp";
import { getUserUuid } from "@/services/user";
import { decreaseCredits, CreditsTransType } from "@/services/credit";
import { calculateVideoCredits } from "@/services/video-credits";
import { createVideoTask, updateVideoTask } from "@/models/video-generation";
import { kling } from "@/aisdk/kling";

export async function POST(req: NextRequest) {
  try {
    const {
      mode,
      model,
      prompt,
      parameters
    } = await req.json();

    // 1. 用户认证
    const user_uuid = await getUserUuid();
    if (!user_uuid) {
      return respErr("用户未认证");
    }

    // 2. 参数验证
    if (!prompt?.trim()) {
      return respErr("提示词不能为空");
    }

    if (!['kling-v1', 'kling-v1-6'].includes(model)) {
      return respErr("不支持的模型");
    }

    // 3. 计算Credits
    const creditsRequired = calculateVideoCredits(
      model,
      parameters.duration || 5,
      parameters.resolution || '720p',
      'std',
      parameters.outputCount || 1
    );

    // 4. 检查并扣除Credits
    try {
      await decreaseCredits({
        user_uuid,
        trans_type: CreditsTransType.VideoGeneration,
        credits: creditsRequired,
      });
    } catch (error) {
      return respErr("积分不足");
    }

    // 5. 创建数据库任务记录
    const taskRecord = await createVideoTask({
      user_uuid,
      mode,
      model,
      prompt,
      parameters,
      status: 'pending',
      credits_used: creditsRequired
    });

    // 6. 调用Kling API创建任务
    try {
      const client = await kling.video(model).getClient();
      
      const klingTask = await client.createTask({
        model,
        prompt,
        negative_prompt: parameters.negativePrompt,
        duration: parameters.duration || 5,
        aspect_ratio: parameters.resolution === "720p" ? "16:9" : "16:9",
        mode: "std"
      });

      if (!klingTask.data?.task_id) {
        throw new Error(klingTask.message || "Kling任务创建失败");
      }

      // 7. 更新数据库记录
      await updateVideoTask(taskRecord.id, {
        kling_task_id: klingTask.data.task_id,
        status: 'processing',
        kling_status: 'pending',
        kling_created_at: new Date()
      });

      // 8. 记录日志
      await logTaskStatusChange(taskRecord.id, {
        old_status: 'pending',
        new_status: 'processing',
        kling_status: 'pending',
        kling_response: klingTask
      });

      return respData({
        taskId: taskRecord.id,
        klingTaskId: klingTask.data.task_id,
        estimatedTime: getEstimatedTime(model, parameters.duration),
        creditsUsed: creditsRequired
      });

    } catch (error) {
      console.error("Kling API调用失败:", error);
      
      // 更新任务状态为失败
      await updateVideoTask(taskRecord.id, {
        status: 'failed',
        error_message: error.message,
        error_code: 'KLING_API_ERROR'
      });

      // 退还积分
      await refundCredits(user_uuid, creditsRequired, taskRecord.id);

      return respErr("视频生成任务创建失败");
    }

  } catch (error) {
    console.error("生成API错误:", error);
    return respErr("服务器内部错误");
  }
}

// 辅助函数
function getEstimatedTime(model: string, duration: number): number {
  // 根据模型和时长估算完成时间(秒)
  const baseTime = model === 'kling-v1-6' ? 180 : 120; // 3分钟 vs 2分钟
  const durationMultiplier = duration === 10 ? 1.5 : 1;
  return Math.floor(baseTime * durationMultiplier);
}

async function logTaskStatusChange(taskId: string, logData: any) {
  // 记录状态变更日志
  try {
    await createTaskLog({
      task_id: taskId,
      ...logData,
      api_call_duration: Date.now() - (logData.startTime || Date.now())
    });
  } catch (error) {
    console.error("记录任务日志失败:", error);
  }
}

async function refundCredits(userUuid: string, credits: number, taskId: string) {
  // 退还积分逻辑
  try {
    await increaseCredits({
      user_uuid: userUuid,
      trans_type: CreditsTransType.VideoGenerationRefund,
      credits,
      reference_id: taskId
    });
    
    await updateVideoTask(taskId, {
      credits_refunded: credits
    });
  } catch (error) {
    console.error("积分退还失败:", error);
  }
}
