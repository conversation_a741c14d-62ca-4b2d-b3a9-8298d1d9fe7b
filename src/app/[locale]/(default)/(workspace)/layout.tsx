import { ReactNode } from "react";
import { getTranslations } from "next-intl/server";
import WorkspaceLayout from "@/components/workspace/layout";
import { NavItem } from "@/types/blocks/base";

// AI视频生成器导航配置
const getWorkspaceNavItems = async (): Promise<NavItem[]> => {
  const t = await getTranslations();
  
  return [
    {
      icon: "RiVideoLine",
      title: t("workspace.nav.txt2vid"),
      url: "/workspace/txt2vid",
      is_active: false,
    },
    {
      icon: "RiImageLine", 
      title: t("workspace.nav.img2vid"),
      url: "/workspace/img2vid",
      is_active: false,
    },
    {
      icon: "RiUserLine",
      title: t("workspace.nav.avatar"),
      url: "/workspace/avatar", 
      is_active: false,
    },
    {
      icon: "RiMotionLine",
      title: t("workspace.nav.motion"),
      url: "/workspace/motion",
      is_active: false,
    },
  ];
};

export default async function WorkspaceRootLayout({
  children,
}: {
  children: ReactNode;
}) {
  const navItems = await getWorkspaceNavItems();

  return (
    <WorkspaceLayout navItems={navItems}>
      {children}
    </WorkspaceLayout>
  );
}
