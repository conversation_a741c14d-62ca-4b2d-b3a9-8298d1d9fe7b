import { getTranslations } from "next-intl/server";
import GeneratorWorkspace from "@/components/workspace/generator-workspace";

export default async function Txt2VidPage() {
  const t = await getTranslations();

  return (
    <GeneratorWorkspace 
      mode="txt2vid"
      title={t("workspace.txt2vid.title")}
      description={t("workspace.txt2vid.description")}
    />
  );
}

export async function generateMetadata() {
  const t = await getTranslations();
  
  return {
    title: t("workspace.txt2vid.title"),
    description: t("workspace.txt2vid.description"),
  };
}
