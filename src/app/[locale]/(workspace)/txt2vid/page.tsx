import { getTranslations } from "next-intl/server";
import GeneratorWorkspace from "@/components/workspace/generator-workspace";

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  const t = await getTranslations();
  let canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/txt2vid`;

  if (locale !== "en") {
    canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/${locale}/txt2vid`;
  }

  return {
    title: t("workspace.txt2vid.title"),
    description: t("workspace.txt2vid.description"),
    alternates: {
      canonical: canonicalUrl,
    },
  };
}

export default async function Txt2VidPage({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  const t = await getTranslations();

  return (
    <GeneratorWorkspace
      mode="txt2vid"
      title={t("workspace.txt2vid.title")}
      description={t("workspace.txt2vid.description")}
    />
  );
}
