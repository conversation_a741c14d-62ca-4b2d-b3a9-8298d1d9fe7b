"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import Icon from "@/components/icon";
import { useTranslations } from "next-intl";

interface GenerateButtonProps {
  onClick?: () => void;
  disabled?: boolean;
  loading?: boolean;
  credits?: number;
  creditsRequired?: number;
  className?: string;
}

export default function GenerateButton({
  onClick,
  disabled = false,
  loading = false,
  credits = 1250,
  creditsRequired = 170,
  className
}: GenerateButtonProps) {
  const t = useTranslations();
  
  // 检查余额是否充足
  const hasEnoughCredits = credits >= creditsRequired;
  const isDisabled = disabled || loading || !hasEnoughCredits;

  return (
    <div className="space-y-3">
      {/* 复用现有Button组件 */}
      <Button
        onClick={onClick}
        disabled={isDisabled}
        className={`w-full h-11 rounded-full font-medium ${className}`}
        size="lg"
      >
        {loading ? (
          <div className="flex items-center space-x-2">
            <Icon name="RiLoader4Line" className="h-4 w-4 animate-spin" />
            <span>{t("workspace.generate.generating")}</span>
          </div>
        ) : (
          <div className="flex items-center space-x-2">
            <Icon name="RiPlayLine" className="h-4 w-4" />
            <span>{t("workspace.generate.button")}</span>
          </div>
        )}
      </Button>

      {/* Credits信息显示 */}
      <div className="flex items-center justify-between text-sm">
        <div className="flex items-center space-x-2">
          <Icon name="RiCoinLine" className="h-4 w-4 text-muted-foreground" />
          <span className="text-muted-foreground">
            {t("workspace.generate.creditsRequired")}:
          </span>
          <Badge 
            variant={hasEnoughCredits ? "secondary" : "destructive"}
            className="text-xs"
          >
            {creditsRequired}
          </Badge>
        </div>

        <div className="flex items-center space-x-2">
          <span className="text-muted-foreground">
            {t("workspace.generate.balance")}:
          </span>
          <Badge 
            variant={hasEnoughCredits ? "secondary" : "destructive"}
            className="text-xs"
          >
            {credits.toLocaleString()}
          </Badge>
        </div>
      </div>

      {/* 余额不足提示 */}
      {!hasEnoughCredits && (
        <div className="flex items-center space-x-2 p-3 bg-destructive/10 border border-destructive/20 rounded-md">
          <Icon name="RiErrorWarningLine" className="h-4 w-4 text-destructive" />
          <span className="text-sm text-destructive">
            {t("workspace.generate.insufficientCredits")}
          </span>
          <Button variant="outline" size="sm" className="ml-auto">
            <Icon name="RiAddLine" className="h-4 w-4 mr-1" />
            {t("workspace.generate.buyCredits")}
          </Button>
        </div>
      )}
    </div>
  );
}
