"use client";

import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { useMediaQuery } from "@/hooks/use-media-query";
import { cn } from "@/lib/utils";
import ParamPanel from "./param-panel";
import PreviewPane from "./preview-pane";
import ModelSelector from "./model-selector";
import PromptInput from "./prompt-input";
import GenerateButton from "./generate-button";
import AdvancedSettings from "./advanced-settings";

interface GeneratorWorkspaceProps {
  mode: "txt2vid" | "img2vid" | "avatar";
  title: string;
  description: string;
}

export default function GeneratorWorkspace({
  mode,
  title,
  description,
}: GeneratorWorkspaceProps) {
  const isMobile = useMediaQuery("(max-width: 1024px)");

  return (
    <div className="flex flex-col h-full">
      {/* Page Header */}
      <div className="flex-shrink-0 p-4 md:p-6 pb-0">
        <div className="mb-4">
          <div className="flex items-center space-x-3 mb-2">
            <h1 className="text-2xl font-bold">{title}</h1>
            <Badge variant="secondary" className="text-xs">
              {mode.toUpperCase()}
            </Badge>
          </div>
          <p className="text-muted-foreground">{description}</p>
        </div>
        <Separator />
      </div>

      {/* Workspace Layout - Fixed Height */}
      <div className="flex-1 min-h-0 p-4 md:p-6 pt-4">
        <div className={cn(
          "flex gap-6 h-full",
          isMobile && "flex-col space-y-6"
        )}>
          {/* Left Panel: Parameters - Fixed Width */}
          <div className={cn(
            "w-96 flex-shrink-0",
            isMobile && "w-full"
          )}>
            <Card className="h-full">
              <ParamPanel mode={mode} />
            </Card>
          </div>

          {/* Right Panel: Preview - Flexible Width, Same Height */}
          <div className="flex-1 min-w-0">
            <Card className="h-full">
              <PreviewPane mode={mode} />
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
