"use client";

import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { useMediaQuery } from "@/hooks/use-media-query";
import { cn } from "@/lib/utils";
import ParamPanel from "./param-panel";
import PreviewPane from "./preview-pane";

interface GeneratorWorkspaceProps {
  mode: "txt2vid" | "img2vid" | "avatar";
  title: string;
  description: string;
}

export default function GeneratorWorkspace({
  mode,
  title,
  description,
}: GeneratorWorkspaceProps) {
  const isMobile = useMediaQuery("(max-width: 1024px)");

  return (
    <div className="h-full p-4 md:p-6 pt-6 md:pt-8">
      {/* Page Header */}
      <div className="mb-6">
        <div className="flex items-center space-x-3 mb-2">
          <h1 className="text-2xl font-bold">{title}</h1>
          <Badge variant="secondary" className="text-xs">
            {mode.toUpperCase()}
          </Badge>
        </div>
        <p className="text-muted-foreground">{description}</p>
      </div>

      <Separator className="mb-6" />

      {/* Workspace Layout */}
      <div className={cn(
        "flex gap-6 h-[calc(100vh-16rem)]",
        isMobile && "flex-col h-auto space-y-6"
      )}>
        {/* Left Panel: Parameters */}
        <div className={cn(
          "min-w-96 max-w-sm",
          isMobile && "min-w-full max-w-full"
        )}>
          <Card className="h-full">
            <ParamPanel mode={mode} />
          </Card>
        </div>

        {/* Right Panel: Preview */}
        <div className="flex-1">
          <Card className="h-full">
            <PreviewPane mode={mode} />
          </Card>
        </div>
      </div>
    </div>
  );
}
