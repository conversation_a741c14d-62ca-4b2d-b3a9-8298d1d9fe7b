"use client";

import { <PERSON> } from "@/i18n/navigation";
import { But<PERSON> } from "@/components/ui/button";
import SignToggle from "@/components/sign/toggle";

export default function WorkspaceHeader() {

  return (
    <header className="sticky top-0 z-40 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container flex h-14 items-center justify-between px-4 md:px-8">
        {/* Left: Brand Name */}
        <div className="flex items-center">
          <Link href="/" className="flex items-center space-x-2">
            <span className="font-bold text-lg">CopyPollo</span>
          </Link>
        </div>

        {/* Right: Login + Start for Free */}
        <div className="flex items-center space-x-3">
          {/* Login */}
          <SignToggle />

          {/* Start for Free Button */}
          <Button variant="default" size="sm" className="rounded-full">
            Start for Free
          </Button>
        </div>
      </div>
    </header>
  );
}
