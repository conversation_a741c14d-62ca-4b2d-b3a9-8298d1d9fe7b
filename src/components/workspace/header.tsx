"use client";

import { <PERSON> } from "@/i18n/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import Icon from "@/components/icon";
import { useTranslations } from "next-intl";
import SignToggle from "@/components/sign/toggle";
import { Separator } from "@/components/ui/separator";

export default function WorkspaceHeader() {
  const t = useTranslations();

  return (
    <header className="sticky top-0 z-40 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container flex h-14 items-center justify-between px-4 md:px-8">
        {/* Left: Logo + Breadcrumb */}
        <div className="flex items-center space-x-4">
          <Link href="/" className="flex items-center space-x-2">
            <Icon name="RiVideoLine" className="h-6 w-6 text-primary" />
            <span className="font-bold text-lg">AI Video</span>
          </Link>
          
          <Separator orientation="vertical" className="h-6" />
          
          <nav className="flex items-center space-x-1 text-sm text-muted-foreground">
            <Link href="/workspace" className="hover:text-foreground">
              {t("workspace.breadcrumb.workspace")}
            </Link>
            <Icon name="RiArrowRightSLine" className="h-4 w-4" />
            <span className="text-foreground font-medium">
              {t("workspace.breadcrumb.generator")}
            </span>
          </nav>
        </div>

        {/* Right: Credits + User Menu */}
        <div className="flex items-center space-x-4">
          {/* Credits Display */}
          <div className="flex items-center space-x-2">
            <Badge variant="secondary" className="flex items-center space-x-1">
              <Icon name="RiCoinLine" className="h-3 w-3" />
              <span>1,250</span>
            </Badge>
            <Button variant="outline" size="sm">
              <Icon name="RiAddLine" className="h-4 w-4 mr-1" />
              {t("workspace.header.buyCredits")}
            </Button>
          </div>

          <Separator orientation="vertical" className="h-6" />

          {/* User Menu */}
          <SignToggle />
        </div>
      </div>
    </header>
  );
}
