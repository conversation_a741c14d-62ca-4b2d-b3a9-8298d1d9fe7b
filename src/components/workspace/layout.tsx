"use client";

import { ReactNode } from "react";
import { NavItem } from "@/types/blocks/base";
import WorkspaceHeader from "./header";
import WorkspaceSidebar from "./sidebar";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import { Button } from "@/components/ui/button";
import Icon from "@/components/icon";
import { useMediaQuery } from "@/hooks/use-media-query";

interface WorkspaceLayoutProps {
  children: ReactNode;
  navItems: NavItem[];
}

export default function WorkspaceLayout({ 
  children, 
  navItems 
}: WorkspaceLayoutProps) {
  const isMobile = useMediaQuery("(max-width: 768px)");

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <WorkspaceHeader />
      
      <div className="flex">
        {/* Desktop Sidebar */}
        {!isMobile && (
          <WorkspaceSidebar navItems={navItems} />
        )}
        
        {/* Mobile Sidebar */}
        {isMobile && (
          <Sheet>
            <SheetTrigger asChild>
              <Button 
                variant="ghost" 
                size="icon"
                className="fixed top-4 left-4 z-50 md:hidden"
              >
                <Icon name="RiMenuLine" className="h-5 w-5" />
              </Button>
            </SheetTrigger>
            <SheetContent side="left" className="w-72 p-0">
              <WorkspaceSidebar navItems={navItems} isMobile />
            </SheetContent>
          </Sheet>
        )}
        
        {/* Main Content */}
        <main className="flex-1 min-h-[calc(100vh-4rem)]">
          {children}
        </main>
      </div>
    </div>
  );
}
