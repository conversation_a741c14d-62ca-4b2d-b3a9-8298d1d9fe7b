"use client";

import { ReactNode } from "react";
import { NavItem } from "@/types/blocks/base";
import WorkspaceHeader from "./header";
import WorkspaceSidebar from "./sidebar";

interface WorkspaceLayoutProps {
  children: ReactNode;
  navItems: NavItem[];
}

export default function WorkspaceLayout({
  children,
  navItems
}: WorkspaceLayoutProps) {

  return (
    <div className="min-h-screen bg-background">
      {/* Fixed Header */}
      <WorkspaceHeader />

      {/* Fixed Left Sidebar */}
      <WorkspaceSidebar navItems={navItems} />

      {/* Main Content - Fixed height with left margin for sidebar */}
      <main className="ml-24 pt-[57px] h-screen overflow-hidden">
        <div className="h-full p-6">
          {children}
        </div>
      </main>
    </div>
  );
}
