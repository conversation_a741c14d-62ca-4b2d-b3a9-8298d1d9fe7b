"use client";

import { ReactNode } from "react";
import { NavItem } from "@/types/blocks/base";
import WorkspaceHeader from "./header";
import WorkspaceSidebar from "./sidebar";

interface WorkspaceLayoutProps {
  children: ReactNode;
  navItems: NavItem[];
}

export default function WorkspaceLayout({
  children,
  navItems
}: WorkspaceLayoutProps) {

  return (
    <div className="min-h-screen bg-background">
      {/* Fixed Header */}
      <WorkspaceHeader />

      {/* Fixed Left Sidebar */}
      <WorkspaceSidebar navItems={navItems} />

      {/* Main Content - Scrollable with left margin for sidebar */}
      <main className="ml-24 pt-[57px] min-h-screen overflow-y-auto">
        {children}
      </main>
    </div>
  );
}
