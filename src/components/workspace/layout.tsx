"use client";

import { ReactNode } from "react";
import WorkspaceHeader from "./header";

interface WorkspaceLayoutProps {
  children: ReactNode;
  navItems?: any[]; // 保留接口兼容性，但不使用
}

export default function WorkspaceLayout({
  children
}: WorkspaceLayoutProps) {

  return (
    <div className="min-h-screen bg-background">
      {/* Fixed Header */}
      <WorkspaceHeader />

      {/* Main Content - Full Width, Scrollable */}
      <main className="w-full">
        {children}
      </main>
    </div>
  );
}
