"use client";

import { ReactNode } from "react";
import { NavItem } from "@/types/blocks/base";
import WorkspaceHeader from "./header";
import WorkspaceSidebar from "./sidebar";

interface WorkspaceLayoutProps {
  children: ReactNode;
  navItems: NavItem[];
}

export default function WorkspaceLayout({
  children,
  navItems
}: WorkspaceLayoutProps) {

  return (
    <div className="min-h-screen bg-background">
      {/* Fixed Header */}
      <WorkspaceHeader />

      {/* Layout with Fixed Sidebar + Scrollable Main */}
      <div className="flex pt-14">
        {/* Fixed Left Sidebar */}
        <WorkspaceSidebar navItems={navItems} />

        {/* Main Content - Scrollable */}
        <main className="flex-1 overflow-y-auto">
          {children}
        </main>
      </div>
    </div>
  );
}
