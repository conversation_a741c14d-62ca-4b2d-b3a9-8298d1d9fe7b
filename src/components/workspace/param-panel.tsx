"use client";

import { useState } from "react";
import { <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/scroll-area";
import { <PERSON><PERSON><PERSON><PERSON>, CardContent } from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import { useTranslations } from "next-intl";
import { Badge } from "@/components/ui/badge";
import Icon from "@/components/icon";
import ModelSelector from "./model-selector";
import PromptInput from "./prompt-input";
import GenerateButton from "./generate-button";
import AdvancedSettings from "./advanced-settings";

interface ParamPanelProps {
  mode: "txt2vid" | "img2vid" | "avatar";
  title?: string;
}

export default function ParamPanel({ mode, title }: ParamPanelProps) {
  const t = useTranslations();

  // 表单状态管理
  const [selectedModel, setSelectedModel] = useState("pollo-1.6");
  const [prompt, setPrompt] = useState("");
  const [resolution, setResolution] = useState("720p");
  const [duration, setDuration] = useState(8);
  const [seed, setSeed] = useState("");
  const [generateAudio, setGenerateAudio] = useState(true);
  const [negativePrompt, setNegativePrompt] = useState("");
  const [outputCount, setOutputCount] = useState(1);
  const [publicVisibility, setPublicVisibility] = useState(true);
  const [copyProtection, setCopyProtection] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);

  // 生成处理函数
  const handleGenerate = async () => {
    setIsGenerating(true);
    try {
      // TODO: 调用生成API
      console.log("Generating video with:", {
        mode,
        model: selectedModel,
        prompt,
        resolution,
        duration,
        seed,
        generateAudio,
        negativePrompt,
        outputCount,
        publicVisibility,
        copyProtection
      });

      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 3000));
    } catch (error) {
      console.error("Generation failed:", error);
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <div className="flex flex-col h-full">
      {/* Fixed Header */}
      <CardHeader className="flex-shrink-0 pb-3 px-4 pt-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold">
            {title || t("workspace.paramPanel.title")}
          </h3>
          <Badge variant="outline" className="text-xs">
            <Icon name="RiSettings3Line" className="w-3 h-3 mr-1" />
            {mode.toUpperCase()}
          </Badge>
        </div>
      </CardHeader>

      {/* Scrollable Content */}
      <CardContent className="flex-1 min-h-0 p-0 flex flex-col">
        <ScrollArea className="flex-1">
          <div className="px-4 pb-3 space-y-4">
            {/* Model Selection */}
            <div className="space-y-3">
              <h4 className="text-sm font-medium">
                {t("workspace.paramPanel.model")}
              </h4>
              <ModelSelector
                value={selectedModel}
                onValueChange={setSelectedModel}
                placeholder="Select AI model"
              />
            </div>

            {/* Prompt Input */}
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <h4 className="text-sm font-medium">
                  {t("workspace.paramPanel.prompt")}
                </h4>
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-muted-foreground">
                    Translate Prompt
                  </span>
                  <Switch
                    checked={true}
                    onCheckedChange={() => {}}
                  />
                </div>
              </div>
              <PromptInput
                value={prompt}
                onChange={setPrompt}
                maxLength={1500}
                showTranslate={false}
                showAIGenerate={true}
              />
            </div>

            {/* Advanced Settings */}
            <AdvancedSettings
              resolution={resolution}
              onResolutionChange={setResolution}
              duration={duration}
              onDurationChange={setDuration}
              seed={seed}
              onSeedChange={setSeed}
              generateAudio={generateAudio}
              onGenerateAudioChange={setGenerateAudio}
              negativePrompt={negativePrompt}
              onNegativePromptChange={setNegativePrompt}
              outputCount={outputCount}
              onOutputCountChange={setOutputCount}
              publicVisibility={publicVisibility}
              onPublicVisibilityChange={setPublicVisibility}
              copyProtection={copyProtection}
              onCopyProtectionChange={setCopyProtection}
            />
          </div>
        </ScrollArea>

        {/* Fixed Generate Button at Bottom */}
        <div className="flex-shrink-0 relative">
          {/* Subtle gradient overlay for smooth transition */}
          <div className="absolute top-0 left-0 right-0 h-4 bg-gradient-to-b from-transparent to-background/50 pointer-events-none" />
          <div className="px-4 py-3">
            <GenerateButton
              onClick={handleGenerate}
              loading={isGenerating}
              disabled={!prompt.trim()}
              credits={1250}
              creditsRequired={170}
            />
          </div>
        </div>
      </CardContent>
    </div>
  );
}
