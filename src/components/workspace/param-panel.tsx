"use client";

import { ScrollArea } from "@/components/ui/scroll-area";
import { <PERSON><PERSON><PERSON>er, CardContent } from "@/components/ui/card";
import { useTranslations } from "next-intl";
import { Badge } from "@/components/ui/badge";
import Icon from "@/components/icon";

interface ParamPanelProps {
  mode: "txt2vid" | "img2vid" | "avatar";
}

export default function ParamPanel({ mode }: ParamPanelProps) {
  const t = useTranslations();

  return (
    <div className="flex flex-col h-full">
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold">
            {t("workspace.paramPanel.title")}
          </h3>
          <Badge variant="outline" className="text-xs">
            <Icon name="RiSettings3Line" className="w-3 h-3 mr-1" />
            {mode}
          </Badge>
        </div>
        <p className="text-sm text-muted-foreground">
          {t("workspace.paramPanel.description")}
        </p>
      </CardHeader>

      <CardContent className="flex-1 p-0">
        <ScrollArea className="h-full px-6 pb-6">
          <div className="space-y-6">
            {/* Model Selection Placeholder */}
            <div className="space-y-3">
              <h4 className="text-sm font-medium">
                {t("workspace.paramPanel.model")}
              </h4>
              <div className="p-4 border-2 border-dashed border-muted-foreground/25 rounded-lg text-center">
                <Icon name="RiRobotLine" className="w-8 h-8 mx-auto mb-2 text-muted-foreground" />
                <p className="text-sm text-muted-foreground">
                  {t("workspace.paramPanel.modelPlaceholder")}
                </p>
              </div>
            </div>

            {/* Prompt Input Placeholder */}
            <div className="space-y-3">
              <h4 className="text-sm font-medium">
                {t("workspace.paramPanel.prompt")}
              </h4>
              <div className="p-4 border-2 border-dashed border-muted-foreground/25 rounded-lg text-center min-h-32">
                <Icon name="RiEdit2Line" className="w-8 h-8 mx-auto mb-2 text-muted-foreground" />
                <p className="text-sm text-muted-foreground">
                  {t("workspace.paramPanel.promptPlaceholder")}
                </p>
              </div>
            </div>

            {/* Advanced Settings Placeholder */}
            <div className="space-y-3">
              <h4 className="text-sm font-medium">
                {t("workspace.paramPanel.advanced")}
              </h4>
              <div className="p-4 border-2 border-dashed border-muted-foreground/25 rounded-lg text-center">
                <Icon name="RiSettings4Line" className="w-8 h-8 mx-auto mb-2 text-muted-foreground" />
                <p className="text-sm text-muted-foreground">
                  {t("workspace.paramPanel.advancedPlaceholder")}
                </p>
              </div>
            </div>

            {/* Generate Button Placeholder */}
            <div className="pt-4 border-t">
              <div className="p-4 border-2 border-dashed border-primary/25 rounded-lg text-center bg-primary/5">
                <Icon name="RiPlayLine" className="w-8 h-8 mx-auto mb-2 text-primary" />
                <p className="text-sm text-primary font-medium">
                  {t("workspace.paramPanel.generatePlaceholder")}
                </p>
              </div>
            </div>
          </div>
        </ScrollArea>
      </CardContent>
    </div>
  );
}
