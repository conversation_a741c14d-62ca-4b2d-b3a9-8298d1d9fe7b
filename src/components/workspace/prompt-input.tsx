"use client";

import { useState, useCallback } from "react";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import Icon from "@/components/icon";
import { useTranslations } from "next-intl";

interface PromptInputProps {
  value?: string;
  onChange?: (value: string) => void;
  placeholder?: string;
  maxLength?: number;
  showTranslate?: boolean;
  showAIGenerate?: boolean;
}

export default function PromptInput({
  value = "",
  onChange,
  placeholder,
  maxLength = 1500,
  showTranslate = true,
  showAIGenerate = true
}: PromptInputProps) {
  const t = useTranslations();
  const [isTranslateEnabled, setIsTranslateEnabled] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);

  // 字符计数
  const charCount = value.length;
  const isOverLimit = charCount > maxLength;

  // 处理输入变化
  const handleChange = useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value;
    if (newValue.length <= maxLength) {
      onChange?.(newValue);
    }
  }, [onChange, maxLength]);

  // AI生成提示词
  const handleAIGenerate = useCallback(async () => {
    setIsGenerating(true);
    try {
      // TODO: 调用AI生成API
      await new Promise(resolve => setTimeout(resolve, 2000)); // 模拟API调用
      
      // 模拟生成的提示词
      const generatedPrompt = "A cinematic shot of a person walking through a bustling city street at golden hour, with warm sunlight filtering through tall buildings and creating dramatic shadows on the pavement.";
      onChange?.(generatedPrompt);
    } catch (error) {
      console.error("AI generation failed:", error);
    } finally {
      setIsGenerating(false);
    }
  }, [onChange]);

  return (
    <div className="space-y-3">
      {/* 主输入区域 */}
      <div className="relative">
        {/* 翻译开关 - 右上角 */}
        {showTranslate && (
          <div className="absolute top-3 right-3 z-10 flex items-center space-x-2">
            <span className="text-sm text-muted-foreground">
              {t("workspace.prompt.translate")}
            </span>
            <Switch
              checked={isTranslateEnabled}
              onCheckedChange={setIsTranslateEnabled}
            />
          </div>
        )}

        {/* 复用现有Textarea组件 */}
        <Textarea
          value={value}
          onChange={handleChange}
          placeholder={placeholder || t("workspace.prompt.placeholder")}
          className={`min-h-[120px] resize-none pr-32 pb-12 ${
            isOverLimit ? "border-destructive focus-visible:ring-destructive" : ""
          }`}
        />

        {/* AI生成按钮 - 左下角 */}
        {showAIGenerate && (
          <div className="absolute bottom-3 left-3">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleAIGenerate}
              disabled={isGenerating}
              className="flex items-center space-x-2 text-muted-foreground hover:text-foreground"
            >
              {isGenerating ? (
                <Icon name="RiLoader4Line" className="h-4 w-4 animate-spin" />
              ) : (
                <Icon name="RiMagicLine" className="h-4 w-4" />
              )}
              <span>
                {isGenerating
                  ? t("workspace.prompt.generating")
                  : t("workspace.prompt.generateWithAI")
                }
              </span>
            </Button>
          </div>
        )}

        {/* 字符计数 - 右下角 */}
        <div className="absolute bottom-3 right-3">
          <Badge
            variant={isOverLimit ? "destructive" : "secondary"}
            className="text-xs"
          >
            {charCount}/{maxLength}
          </Badge>
        </div>
      </div>

      {/* 提示信息 */}
      {isOverLimit && (
        <p className="text-sm text-destructive">
          {t("workspace.prompt.overLimit")}
        </p>
      )}
    </div>
  );
}
