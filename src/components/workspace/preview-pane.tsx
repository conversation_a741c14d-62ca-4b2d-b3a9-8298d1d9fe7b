"use client";

import { <PERSON><PERSON><PERSON><PERSON>, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { useTranslations } from "next-intl";
import Icon from "@/components/icon";
import { cn } from "@/lib/utils";

interface PreviewPaneProps {
  mode: "txt2vid" | "img2vid" | "avatar";
}

export default function PreviewPane({ mode }: PreviewPaneProps) {
  const t = useTranslations();

  return (
    <div className="flex flex-col h-full">
      {/* Fixed Header */}
      <CardHeader className="flex-shrink-0 pb-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold">
            Sample Video
          </h3>
          <div className="flex items-center space-x-2">
            <Badge variant="secondary" className="text-xs">
              <Icon name="RiEyeLine" className="w-3 h-3 mr-1" />
              Preview
            </Badge>
            <Button variant="ghost" size="sm">
              <Icon name="RiFullscreenLine" className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </CardHeader>

      {/* Flexible Content */}
      <CardContent className="flex-1 min-h-0 p-6">
        <div className="flex flex-col h-full">
          {/* Video Preview Area */}
          <div className="flex-1 flex items-center justify-center">
            <div className="w-full max-w-2xl aspect-video bg-muted/30 rounded-lg border-2 border-dashed border-muted-foreground/25 flex flex-col items-center justify-center">
              <Icon name="RiVideoLine" className="w-16 h-16 text-muted-foreground mb-4" />
              <h4 className="text-lg font-medium text-muted-foreground mb-2">
                {t("workspace.previewPane.noVideo")}
              </h4>
              <p className="text-sm text-muted-foreground text-center max-w-sm">
                {t("workspace.previewPane.noVideoDescription")}
              </p>
            </div>
          </div>

          {/* Progress/Results Area */}
          <div className="mt-6 pt-6 border-t">
            <div className="flex items-center justify-between mb-4">
              <h4 className="text-sm font-medium">
                {t("workspace.previewPane.results")}
              </h4>
              <Badge variant="outline" className="text-xs">
                0 / 4
              </Badge>
            </div>

            {/* Results Grid Placeholder */}
            <div className="grid grid-cols-2 lg:grid-cols-4 gap-3">
              {Array.from({ length: 4 }).map((_, index) => (
                <div
                  key={index}
                  className={cn(
                    "aspect-video bg-muted/20 rounded-md border border-dashed border-muted-foreground/20",
                    "flex items-center justify-center"
                  )}
                >
                  <Icon name="RiImageLine" className="w-6 h-6 text-muted-foreground/50" />
                </div>
              ))}
            </div>

            {/* Action Buttons */}
            <div className="flex items-center justify-center space-x-2 mt-4">
              <Button variant="outline" size="sm" disabled>
                <Icon name="RiDownloadLine" className="w-4 h-4 mr-2" />
                {t("workspace.previewPane.download")}
              </Button>
              <Button variant="outline" size="sm" disabled>
                <Icon name="RiHeartLine" className="w-4 h-4 mr-2" />
                {t("workspace.previewPane.favorite")}
              </Button>
              <Button variant="outline" size="sm" disabled>
                <Icon name="RiShareLine" className="w-4 h-4 mr-2" />
                {t("workspace.previewPane.share")}
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </div>
  );
}
