{"metadata": {"title": "几小时内构建任何 AI SaaS 创业项目 | ShipAny", "description": "ShipAny 是一个用于构建 AI SaaS 创业项目的 NextJS 模板，提供各种模板和组件，帮助您快速启动。", "keywords": "Ship<PERSON>ny, AI SaaS 模板, NextJS 模板"}, "user": {"sign_in": "登录", "sign_out": "退出登录", "credits": "额度", "api_keys": "API 密钥", "my_orders": "我的订单", "user_center": "用户中心", "admin_system": "管理后台"}, "sign_modal": {"sign_in_title": "登录", "sign_in_description": "登录您的账户", "sign_up_title": "注册", "sign_up_description": "创建新账户", "email_title": "邮箱", "email_placeholder": "请输入您的邮箱", "password_title": "密码", "password_placeholder": "请输入您的密码", "forgot_password": "忘记密码？", "or": "或", "continue": "继续", "no_account": "还没有账户？", "email_sign_in": "使用邮箱登录", "google_sign_in": "使用 Google 登录", "github_sign_in": "使用 GitHub 登录", "close_title": "关闭", "cancel_title": "取消"}, "my_orders": {"title": "我的订单", "description": "在 ShipAny 上购买的订单。", "no_orders": "未找到订单", "tip": "", "activate_order": "激活订单", "actived": "已激活", "join_discord": "加入 Discord", "read_docs": "阅读文档", "table": {"order_no": "订单号", "email": "邮箱", "product_name": "产品名称", "amount": "金额", "paid_at": "支付时间", "github_username": "GitHub 用户名", "status": "状态", "manage_billing": "管理账单", "interval": "周期", "interval_month": "月付", "interval_year": "年付", "interval_one_time": "一次性"}}, "my_credits": {"title": "我的积分", "left_tip": "剩余积分: {left_credits}", "no_credits": "没有积分记录", "recharge": "充值", "table": {"trans_no": "交易号", "trans_type": "交易类型", "credits": "积分", "updated_at": "更新时间", "status": "状态", "expired_at": "过期时间", "created_at": "创建时间"}}, "api_keys": {"title": "API 密钥", "tip": "请妥善保管您的 API 密钥，避免泄露", "no_api_keys": "没有 API 密钥", "create_api_key": "创建 API 密钥", "table": {"name": "名称", "key": "密钥", "created_at": "创建时间"}, "form": {"name": "名称", "name_placeholder": "API 密钥名称", "submit": "提交"}}, "blog": {"title": "博客", "description": "关于 ShipAny 的新闻、资源和更新", "read_more_text": "阅读更多", "no_content": "暂无内容", "all": "全部", "category": "分类", "author": "作者"}, "my_invites": {"title": "我的邀请", "description": "查看您的邀请记录", "no_invites": "未找到邀请记录", "my_invite_link": "我的邀请链接", "edit_invite_link": "编辑邀请链接", "copy_invite_link": "复制邀请链接", "invite_code": "邀请码", "invite_tip": "每邀请 1 位朋友购买 ShipAny，奖励 $50。", "invite_balance": "邀请奖励余额", "total_invite_count": "总邀请人数", "total_paid_count": "已充值人数", "total_award_amount": "总奖励金额", "update_invite_code": "设置邀请码", "update_invite_code_tip": "输入你的自定义邀请码", "update_invite_button": "保存", "no_orders": "你需要先购买过 ShipAny 才能邀请朋友", "no_affiliates": "你暂无邀请朋友的权限，请联系我们申请开通。", "table": {"invite_time": "邀请时间", "invite_user": "邀请用户", "status": "状态", "reward_percent": "奖励比例", "reward_amount": "奖励金额", "pending": "已注册，未支付", "completed": "已支付"}}, "feedback": {"title": "反馈", "description": "我们很乐意听取您对产品的看法或如何改进产品体验。", "submit": "提交", "loading": "提交中...", "contact_tip": "其他联系方式", "rating_tip": "您对 ShipAny 的看法如何？", "placeholder": "在这里留下您的反馈..."}, "workspace": {"nav": {"txt2vid": "文本生成视频", "img2vid": "图片生成视频", "avatar": "虚拟人视频", "motion": "动态视频"}, "breadcrumb": {"workspace": "工作台", "generator": "AI 生成器"}, "header": {"buyCredits": "购买积分"}, "txt2vid": {"title": "文本生成视频", "description": "使用 AI 将您的文本提示转换为精美的视频"}, "paramPanel": {"title": "参数设置", "description": "配置您的视频生成设置", "model": "AI 模型", "modelPlaceholder": "模型选择器即将推出", "prompt": "文本提示", "promptPlaceholder": "提示词输入即将推出", "advanced": "高级设置", "advancedPlaceholder": "高级选项即将推出", "generatePlaceholder": "生成按钮即将推出"}, "previewPane": {"title": "预览与结果", "noVideo": "尚未生成视频", "noVideoDescription": "处理完成后，您生成的视频将在此处显示", "results": "生成结果", "download": "下载", "favorite": "收藏", "share": "分享"}, "generate": {"button": "生成", "generating": "生成中...", "creditsRequired": "所需积分", "balance": "余额", "insufficientCredits": "积分不足，无法生成", "buyCredits": "购买积分"}, "prompt": {"placeholder": "描述您想要创建的视频...", "translate": "翻译为英文", "generateWithAI": "AI 生成", "generating": "生成中...", "overLimit": "超出字符限制"}, "advanced": {"title": "高级设置", "generateAudio": "生成音频", "generateAudioDesc": "为您的视频生成音频", "resolution": "分辨率", "videoLength": "视频长度", "seed": "种子值", "negativePrompt": "负面提示词", "optional": "可选", "negativePromptPlaceholder": "您不希望在视频中看到的内容...", "outputVideoNumber": "输出视频数量", "publicVisibility": "公开可见", "publicVisibilityDesc": "让您的视频对公众可见", "copyProtection": "版权保护", "copyProtectionDesc": "保护您的视频免受未经授权的复制"}}}