{"metadata": {"title": "Ship Any AI SaaS Startups in hours | ShipAny", "description": "ShipAny is a NextJS boilerplate for building AI SaaS startups, Ship Fast with a variety of templates and components.", "keywords": "ShipAny, AI SaaS Boilerplate, NextJS Boilerplate"}, "user": {"sign_in": "Sign In", "sign_out": "Sign Out", "credits": "Credits", "api_keys": "API Keys", "my_orders": "My Orders", "user_center": "User Center", "admin_system": "Admin System"}, "sign_modal": {"sign_in_title": "Sign In", "sign_in_description": "Sign in to your account", "sign_up_title": "Sign Up", "sign_up_description": "Create an account", "email_title": "Email", "email_placeholder": "Input your email here", "password_title": "Password", "password_placeholder": "Input your password here", "forgot_password": "Forgot password?", "or": "Or", "continue": "Continue", "no_account": "Don't have an account?", "email_sign_in": "Sign in with <PERSON><PERSON>", "google_sign_in": "Sign in with Google", "github_sign_in": "Sign in with GitHub", "close_title": "Close", "cancel_title": "Cancel"}, "my_orders": {"title": "My Orders", "description": "orders paid with ShipAny.", "no_orders": "No orders found", "tip": "", "activate_order": "Activate Order", "actived": "Activated", "join_discord": "Join <PERSON>", "read_docs": "Read Docs", "table": {"order_no": "Order No", "email": "Email", "product_name": "Product Name", "amount": "Amount", "paid_at": "<PERSON><PERSON>", "github_username": "GitHub Username", "status": "Status", "manage_billing": "Manage Billing", "interval": "Interval", "interval_month": "Monthly", "interval_year": "Annually", "interval_one_time": "One-Time"}}, "my_credits": {"title": "My Credits", "left_tip": "left credits: {left_credits}", "no_credits": "No credits records", "recharge": "Recharge", "table": {"trans_no": "Trans No", "trans_type": "Trans Type", "credits": "Credits", "updated_at": "Updated At", "status": "Status", "expired_at": "Expired At", "created_at": "Created At"}}, "api_keys": {"title": "API Keys", "tip": "Please keep your apikey safe to avoid leaks", "no_api_keys": "No API Keys", "create_api_key": "Create API Key", "table": {"name": "Name", "key": "Key", "created_at": "Created At"}, "form": {"name": "Name", "name_placeholder": "API Key Name", "submit": "Submit"}}, "blog": {"title": "Blog", "description": "News, resources, and updates about ShipAny", "read_more_text": "Read More", "no_content": "No content", "all": "All", "category": "Category", "author": "Author"}, "my_invites": {"title": "My Invites", "description": "View your invite records", "no_invites": "No invite records found", "my_invite_link": "My Invite Link", "edit_invite_link": "Edit Invite <PERSON>", "copy_invite_link": "Copy Invite Link", "invite_code": "Invite Code", "invite_tip": "Invite 1 friend to buy ShipAny, reward $50.", "invite_balance": "In<PERSON>te <PERSON>", "total_invite_count": "Total Invite Count", "total_paid_count": "Total Paid Count", "total_award_amount": "Total Award Amount", "update_invite_code": "Set Invite Code", "update_invite_code_tip": "Input your custom invite code", "update_invite_button": "Save", "no_orders": "You can't invite others before you bought ShipAny", "no_affiliates": "You're not allowed to invite others, please contact us to apply for permission.", "table": {"invite_time": "Invite Time", "invite_user": "Invite User", "status": "Status", "reward_percent": "<PERSON><PERSON> Percent", "reward_amount": "<PERSON><PERSON> Amount", "pending": "Pending", "completed": "Completed"}}, "feedback": {"title": "<PERSON><PERSON><PERSON>", "description": "We'd love to hear what went well or how we can improve the product experience.", "submit": "Submit", "loading": "Submitting...", "contact_tip": "Other ways to contact us", "rating_tip": "How do you feel about <PERSON><PERSON><PERSON>?", "placeholder": "Leave your words here..."}, "workspace": {"nav": {"txt2vid": "Text to Video", "img2vid": "Image to Video", "avatar": "Avatar Video", "motion": "Motion Video"}, "breadcrumb": {"workspace": "Workspace", "generator": "AI Generator"}, "header": {"buyCredits": "Buy Credits"}, "txt2vid": {"title": "Text to Video Generator", "description": "Transform your text prompts into stunning videos with AI"}, "paramPanel": {"title": "Parameters", "description": "Configure your video generation settings", "model": "AI Model", "modelPlaceholder": "Model selector coming soon", "prompt": "Text Prompt", "promptPlaceholder": "Prompt input coming soon", "advanced": "Advanced Settings", "advancedPlaceholder": "Advanced options coming soon", "generatePlaceholder": "Generate button coming soon"}, "previewPane": {"title": "Preview & Results", "noVideo": "No video generated yet", "noVideoDescription": "Your generated video will appear here once processing is complete", "results": "Generated Results", "download": "Download", "favorite": "Favorite", "share": "Share"}, "generate": {"button": "Generate", "generating": "Generating...", "creditsRequired": "Credits required", "balance": "Balance", "insufficientCredits": "Insufficient credits to generate", "buyCredits": "Buy Credits"}, "prompt": {"placeholder": "Describe the video you want to create...", "translate": "Translate to English", "generateWithAI": "Generate with AI", "generating": "Generating...", "overLimit": "Character limit exceeded"}, "advanced": {"title": "Advanced", "generateAudio": "Generate Audio", "generateAudioDesc": "Generate audio for your video", "resolution": "Resolution", "videoLength": "Video Length", "seed": "Seed", "negativePrompt": "Negative Prompt", "optional": "Optional", "negativePromptPlaceholder": "What you don't want to see in the video...", "outputVideoNumber": "Output Video Number", "publicVisibility": "Public Visibility", "publicVisibilityDesc": "Make your video visible to the public", "copyProtection": "Copy Protection", "copyProtectionDesc": "Protect your video from unauthorized copying"}}}