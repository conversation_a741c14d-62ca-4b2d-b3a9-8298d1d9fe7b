# AI Video Generator 复刻开发 TODOLIST

基于 Pollo.ai Video Generator 的完整复刻计划，遵循 ShipAny 项目代码规范。

## 📋 项目概述

**目标**: 复刻 Pollo.ai 的 AI 视频生成器功能
**技术栈**: Next.js 19 + shadcn/ui + Tailwind CSS + Drizzle ORM
**架构**: 工作区型页面，支持多种生成模式（Txt2Vid, Img2Vid, Avatar等）

---

## 🏗️ 阶段一：基础架构搭建

### [ ] 1.1 页面布局框架 🔄 **复用现有组件**
- [ ] 创建 `src/app/[locale]/(workspace)` 路由组
- [ ] **复用 `ConsoleLayout`** 作为 AppShell 基础
  - [ ] 扩展 TopBar: Logo + 面包屑 + 账户菜单 + CTA按钮
  - [ ] 保持固定高度 `h-14`
- [ ] **复用 `SidebarNav`** 组件
  - [ ] 调整为固定宽度 `w-18` (72px)
  - [ ] 使用现有图标 + 标签布局
  - [ ] 利用现有路由高亮逻辑
  - [ ] **复用 `Sheet`** 实现移动端抽屉

### [ ] 1.2 工作区域布局 🔄 **复用现有组件**
- [ ] **复用 `Card`** 创建 WorkspaceArea 组件
- [ ] **复用 `Card`** 实现 ParamPanel (左侧参数面板)
  - [ ] 最小宽度 `min-w-96` (24rem)
  - [ ] 最大宽度 `max-w-sm`
  - [ ] 垂直滚动 `overflow-y-auto`
- [ ] **复用 `Card`** 实现 PreviewPane (右侧预览面板)
  - [ ] 弹性布局 `flex-1`
  - [ ] 视频居中显示
  - [ ] 底部进度/结果网格区域

### [ ] 1.3 响应式适配 🔄 **复用现有组件**
- [ ] 移动端 `≤ md` 断点适配
- [ ] **复用现有 SidebarNav** 响应式逻辑
- [ ] **复用 `Sheet`** 实现 ParamPanel 抽屉
- [ ] PreviewPane 全屏显示

---

## 🧩 阶段二：核心组件开发

### [ ] 2.1 导航组件 🔄 **复用现有组件**
- [ ] **直接复用 `SidebarNav`** 组件
  - [ ] 已支持图标、标签、链接功能
  - [ ] 已支持 JSON 数据驱动配置
  - [ ] 已有活跃状态样式逻辑
- [ ] 导航数据配置 (复用现有 NavItem 类型)
  ```typescript
  const navItems: NavItem[] = [
    { icon: "RiVideoLine", title: "Txt2Vid", url: "/workspace/txt2vid" },
    { icon: "RiImageLine", title: "Img2Vid", url: "/workspace/img2vid" },
    { icon: "RiUserLine", title: "Avatar", url: "/workspace/avatar" },
    // ...
  ]
  ```

### [ ] 2.2 表单组件 🔄 **复用现有组件**
- [ ] **复用 `FormBlock`** 作为 GeneratorForm 主表单
  - [ ] 利用现有字段映射机制
  - [ ] 复用现有表单验证 (react-hook-form + zod)
- [ ] **复用 `Select` + `Dialog`** 实现 ModelSelector
  - [ ] 使用现有 Select 组件
  - [ ] 模型 Logo 显示 (复用 Icon 组件)
  - [ ] 速度说明文本 "30% Faster..."
- [ ] **复用 `Textarea`** 实现 PromptInput
  - [ ] 使用现有 Textarea 组件
  - [ ] 1500字符限制
  - [ ] Token 计数显示
  - [ ] **复用 `Switch`** 实现翻译开关

### [ ] 2.3 高级设置组件 🔄 **复用现有组件**
- [ ] **复用 `Accordion`** 实现高级设置折叠面板
  - [ ] 使用现有 Accordion 组件
  - [ ] 默认折叠状态
  - [ ] 已有平滑展开动画
- [ ] **复用 `ToggleGroup`** 实现分辨率选择
  - [ ] 720p / 1080p pill 切换
  - [ ] 使用现有 ToggleGroup 组件
- [ ] **复用 `Input[type="range"]`** 实现视频长度滑块
  - [ ] 使用现有 Input 组件
  - [ ] 步长 1s，标签显示 "8s"
- [ ] **复用 `Input`** 实现种子值输入
  - [ ] 空值时自动随机生成

### [ ] 2.4 生成与预览组件 🔄 **复用现有组件**
- [ ] **复用 `Button`** 实现生成按钮
  - [ ] Credits 消耗计算显示 (复用 Badge)
  - [ ] 禁用状态处理
  - [ ] framer-motion 点击动画
- [ ] **使用原生 `<video>` + `Card`** 实现预览播放器
  - [ ] 支持 poster 封面
  - [ ] 原生控制条
  - [ ] 全屏播放支持
- [ ] **复用 `Skeleton` + `Badge`** 实现进度覆盖层
  - [ ] 使用现有 Skeleton 组件
  - [ ] Badge 显示百分比进度
- [ ] **复用 `Card` 网格** 实现结果展示
  - [ ] 1-4 卡片布局
  - [ ] **复用 `Button`** 实现收藏/下载/分享
  - [ ] R2 链接支持

---

## 🔄 阶段三：状态管理与数据流

### [ ] 3.1 状态管理
- [ ] 创建 `stores/generator.ts` (Zustand)
  - [ ] 全局生成状态
  - [ ] 多模式共享状态
- [ ] 创建 `hooks/useGenerate.ts`
  - [ ] 生成任务管理
  - [ ] 状态轮询逻辑
  - [ ] 错误处理

### [ ] 3.2 API 集成
- [ ] 创建 `/api/video/generate` 端点
  - [ ] 调用 Pollo API
  - [ ] 任务创建与存储
- [ ] 创建 `/api/video/status` 端点
  - [ ] 任务状态查询
  - [ ] 支持批量查询
- [ ] 实现实时进度更新
  - [ ] SWR 轮询或 WebSocket
  - [ ] 进度状态同步

### [ ] 3.3 数据库设计
- [ ] 扩展数据库 Schema
  ```sql
  -- 生成任务表
  CREATE TABLE cp_generation_jobs (
    id SERIAL PRIMARY KEY,
    uuid VARCHAR(255) UNIQUE NOT NULL,
    user_uuid VARCHAR(255) NOT NULL,
    mode VARCHAR(50) NOT NULL, -- txt2vid, img2vid, avatar
    model VARCHAR(100) NOT NULL,
    prompt TEXT,
    parameters JSONB,
    status VARCHAR(50) NOT NULL, -- pending, processing, completed, failed
    result_url VARCHAR(500),
    credits_cost INTEGER NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE
  );
  ```

---

## 🎨 阶段四：样式与主题

### [ ] 4.1 主题配置
- [ ] 使用现有 pollo-dark CSS 变量
  - [ ] 背景色: `hsl(var(--background))` (#0B0D14)
  - [ ] 主色: `hsl(var(--primary))` (#E5007D)
  - [ ] 辅色: `hsl(var(--secondary))` (#06B6D4)

### [ ] 4.2 组件样式
- [ ] SideNav: `w-18`, TopBar: `h-14`
- [ ] ParamPanel: `max-w-sm`
- [ ] 按钮: `h-11 px-6 rounded-full`
- [ ] 卡片: `bg-card/75 backdrop-blur border border-border`
- [ ] 悬停效果: `hover:shadow-lg`

### [ ] 4.3 动画效果
- [ ] shadcn Accordion 默认动画
- [ ] 生成按钮 framer-motion scale-tap
- [ ] 页面切换过渡动画

---

## 🔧 阶段五：交互细节实现

### [ ] 5.1 模型选择交互
- [ ] 点击展开 Command + Popover
- [ ] 下方说明行显示
- [ ] 键盘导航支持

### [ ] 5.2 提示词增强
- [ ] "Generate with AI" 按钮
- [ ] GPT 辅助图标
- [ ] 点击补全 prompt 功能
- [ ] 调用 GPT API 服务

### [ ] 5.3 Credits 系统
- [ ] Credits 计算逻辑
  ```typescript
  credits = baseCredits[model] * durationMult * ratioMult
  ```
- [ ] 动态颜色显示
  - [ ] 余额充足: 默认颜色
  - [ ] 余额不足: `text-pink-400`
- [ ] 余额不足时禁用生成按钮

---

## 📁 阶段六：文件结构实现

### [ ] 6.1 创建目录结构
```
src/app/[locale]/(workspace)/
├── layout.tsx                 # AppShell 布局
├── page.tsx                   # 默认重定向到 txt2vid
├── txt2vid/
│   └── page.tsx              # Txt2Vid 页面
├── img2vid/
│   └── page.tsx              # Img2Vid 页面
├── avatar/
│   └── page.tsx              # Avatar 页面
└── components/
    ├── GeneratorForm.tsx
    ├── ModelSelector.tsx
    ├── PromptInput.tsx
    ├── AdvancedAccordion.tsx
    ├── PreviewPlayer.tsx
    └── ...
```

### [ ] 6.2 创建页面组件
- [ ] 实现 `GeneratorPage` 通用组件
- [ ] 支持 mode 参数区分不同模式
- [ ] 字段映射配置化

---

## 🧪 阶段七：测试与优化

### [ ] 7.1 功能测试
- [ ] 各模式生成流程测试
- [ ] 响应式布局测试
- [ ] 错误处理测试
- [ ] Credits 扣费测试

### [ ] 7.2 性能优化
- [ ] 组件懒加载
- [ ] 图片/视频预加载
- [ ] API 请求优化
- [ ] 状态更新优化

### [ ] 7.3 用户体验
- [ ] 加载状态优化
- [ ] 错误提示优化
- [ ] 成功反馈优化
- [ ] 键盘快捷键支持

---

## 🚀 阶段八：部署与扩展

### [ ] 8.1 部署准备
- [ ] 环境变量配置
- [ ] 数据库迁移
- [ ] API 密钥配置
- [ ] CDN 资源配置

### [ ] 8.2 扩展功能
- [ ] 批量生成支持
- [ ] 生成历史记录
- [ ] 模板预设功能
- [ ] 社区分享功能

---

## 📝 开发规范

### 🔄 组件复用优先原则
**核心约束：能复用现有组件的，直接复用，避免重复造轮子**

#### 必须复用的现有组件
- **布局组件**
  - `ConsoleLayout` - 控制台布局框架 ✅
  - `SidebarNav` - 侧边栏导航 ✅
  - `Card` - 卡片容器 ✅

- **表单组件**
  - `FormBlock` - 通用表单组件 ✅
  - `Input` - 输入框 ✅
  - `Textarea` - 文本域 ✅
  - `Button` - 按钮 ✅
  - `Select` - 选择器 ✅
  - `Switch` - 开关 ✅
  - `ToggleGroup` - 切换组 ✅

- **UI 基础组件**
  - `Accordion` - 折叠面板 ✅
  - `Badge` - 徽章 ✅
  - `Dialog` - 对话框 ✅
  - `Sheet` - 抽屉 ✅
  - `Tabs` - 标签页 ✅
  - `Tooltip` - 提示框 ✅
  - `Skeleton` - 骨架屏 ✅
  - `Separator` - 分隔线 ✅

- **功能组件**
  - `Icon` - 图标组件 (Remix Icons) ✅
  - `TableComponent` - 表格组件 ✅
  - `Crumb` - 面包屑 ✅
  - `Toolbar` - 工具栏 ✅

#### 组件复用映射表
```typescript
// AI 视频生成器 → 现有组件映射
const COMPONENT_REUSE_MAP = {
  // 布局相关
  AppShell: 'ConsoleLayout',           // 直接复用控制台布局
  SideNav: 'SidebarNav',              // 复用侧边栏导航
  WorkspaceArea: 'div + Card',        // 使用 Card 包装工作区

  // 表单相关
  GeneratorForm: 'FormBlock',         // 复用通用表单
  ModelSelector: 'Select + Dialog',   // 复用选择器 + 对话框
  PromptInput: 'Textarea',            // 复用文本域
  AdvancedAccordion: 'Accordion',     // 复用折叠面板
  ResolutionSelector: 'ToggleGroup',  // 复用切换组
  VideoLengthSlider: 'Input[range]',  // 复用范围输入
  GenerateButton: 'Button',           // 复用按钮

  // 显示相关
  ProgressOverlay: 'Skeleton + Badge', // 复用骨架屏 + 徽章
  OutputGrid: 'div + Card[]',         // 使用卡片网格
  PreviewPlayer: 'video + Card',      // 原生视频 + 卡片包装

  // 交互相关
  CreditsDisplay: 'Badge',            // 复用徽章显示积分
  StatusIndicator: 'Badge',           // 复用徽章显示状态
  ActionMenu: 'DropdownMenu',         // 复用下拉菜单
};
```

### 代码规范
- 遵循 ShipAny 项目现有代码风格
- 使用 TypeScript 严格模式
- 组件采用函数式 + Hooks 模式
- API 路由遵循 RESTful 设计
- **优先复用现有组件，减少新组件开发**

### 命名规范
- 组件: PascalCase (GeneratorForm)
- 文件: kebab-case (generator-form.tsx)
- 变量: camelCase (generatorState)
- 常量: UPPER_SNAKE_CASE (API_ENDPOINTS)

### 提交规范
- feat: 新功能
- fix: 修复
- style: 样式调整
- refactor: 重构
- test: 测试
- docs: 文档

---

## 🎯 里程碑检查点

- [ ] **里程碑 1**: 基础架构完成 (AppShell + 路由)
- [ ] **里程碑 2**: Txt2Vid MVP 完成 (表单 + API)
- [ ] **里程碑 3**: 预览播放完成 (播放器 + 进度)
- [ ] **里程碑 4**: 高级设置完成 (所有参数控制)
- [ ] **里程碑 5**: Credits 系统完成 (计费 + 余额)
- [ ] **里程碑 6**: 响应式完成 (移动端适配)
- [ ] **里程碑 7**: 多模式复用完成 (Img2Vid + Avatar)

---

**预计开发周期**: 4-6 周
**优先级**: 功能完整性 > 用户体验 > 性能优化
**成功标准**: 90% 还原 Pollo.ai 功能，代码复用率 > 80%

---

## ✅ 组件复用检查清单

### 必须复用的现有组件 (禁止重新开发)
- [ ] **布局组件**
  - [ ] `ConsoleLayout` → AppShell 基础框架
  - [ ] `SidebarNav` → 侧边栏导航
  - [ ] `Card` → 所有容器包装

- [ ] **表单组件**
  - [ ] `FormBlock` → 主表单容器
  - [ ] `Input` → 所有输入框
  - [ ] `Textarea` → 提示词输入
  - [ ] `Select` → 模型选择器
  - [ ] `Button` → 所有按钮
  - [ ] `Switch` → 开关控件
  - [ ] `ToggleGroup` → 分辨率选择

- [ ] **交互组件**
  - [ ] `Accordion` → 高级设置折叠
  - [ ] `Dialog` → 模态对话框
  - [ ] `Sheet` → 移动端抽屉
  - [ ] `Tabs` → 标签页切换
  - [ ] `Tooltip` → 提示信息

- [ ] **显示组件**
  - [ ] `Badge` → 状态/积分显示
  - [ ] `Skeleton` → 加载状态
  - [ ] `Icon` → 所有图标 (Remix Icons)
  - [ ] `Separator` → 分隔线
  - [ ] `TableComponent` → 数据表格

### 组件复用验证
开发前必须检查：
1. **是否已有现成组件？** → 直接复用
2. **现有组件是否可扩展？** → 扩展现有组件
3. **确实需要新组件？** → 基于现有组件样式开发

### 禁止重复开发的组件
❌ **严禁重新开发以下组件**：
- 任何表单控件 (Input, Select, Button 等)
- 布局容器 (Card, Dialog, Sheet 等)
- 基础 UI (Badge, Icon, Separator 等)
- 导航组件 (SidebarNav 已完善)

---

## 🔧 技术实现细节

### 组件设计模式
```typescript
// 通用生成器页面组件
interface GeneratorPageProps {
  mode: 'txt2vid' | 'img2vid' | 'avatar';
  config: GeneratorConfig;
}

// 字段映射配置
interface GeneratorConfig {
  fields: FormField[];
  models: ModelOption[];
  defaultValues: Record<string, any>;
  validation: ZodSchema;
}
```

### API 设计规范
```typescript
// 生成请求
POST /api/video/generate
{
  mode: string;
  model: string;
  prompt: string;
  parameters: {
    resolution: '720p' | '1080p';
    duration: number;
    seed?: number;
  };
}

// 状态查询
GET /api/video/status?taskIds=uuid1,uuid2
{
  tasks: Array<{
    id: string;
    status: 'pending' | 'processing' | 'completed' | 'failed';
    progress?: number;
    result?: string;
    error?: string;
  }>;
}
```

### 状态管理架构
```typescript
// Zustand Store
interface GeneratorStore {
  // 状态
  currentMode: GeneratorMode;
  tasks: Map<string, GenerationTask>;

  // 操作
  setMode: (mode: GeneratorMode) => void;
  createTask: (params: GenerationParams) => Promise<string>;
  updateTaskStatus: (id: string, status: TaskStatus) => void;

  // 选择器
  getTasksByStatus: (status: TaskStatus) => GenerationTask[];
  getCurrentTask: () => GenerationTask | null;
}
```

### 数据库扩展
```sql
-- 用户 Credits 记录表
CREATE TABLE cp_credit_transactions (
  id SERIAL PRIMARY KEY,
  user_uuid VARCHAR(255) NOT NULL,
  amount INTEGER NOT NULL, -- 正数为充值，负数为消费
  type VARCHAR(50) NOT NULL, -- purchase, generation, refund
  reference_id VARCHAR(255), -- 关联的订单或任务ID
  description TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 生成模型配置表
CREATE TABLE cp_generation_models (
  id SERIAL PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  mode VARCHAR(50) NOT NULL,
  base_credits INTEGER NOT NULL,
  max_duration INTEGER,
  supported_resolutions JSONB,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

---

## 📚 参考资源

### UI 组件库
- [shadcn/ui Components](https://ui.shadcn.com/docs/components)
- [Radix UI Primitives](https://www.radix-ui.com/primitives)
- [Lucide Icons](https://lucide.dev/icons/)

### 动画库
- [Framer Motion](https://www.framer.com/motion/)
- [Lottie React](https://github.com/Gamote/lottie-react)

### 表单处理
- [React Hook Form](https://react-hook-form.com/)
- [Zod Validation](https://zod.dev/)

### 状态管理
- [Zustand](https://github.com/pmndrs/zustand)
- [SWR](https://swr.vercel.app/)

---

## 🐛 常见问题解决

### 1. 视频播放器兼容性
```typescript
// 使用 video.js 或原生 video 元素
const VideoPlayer = ({ src, poster }: VideoPlayerProps) => {
  return (
    <video
      controls
      poster={poster}
      className="w-full h-auto rounded-lg"
      preload="metadata"
    >
      <source src={src} type="video/mp4" />
      您的浏览器不支持视频播放。
    </video>
  );
};
```

### 2. 大文件上传处理
```typescript
// 分片上传实现
const uploadLargeFile = async (file: File) => {
  const chunkSize = 1024 * 1024; // 1MB
  const chunks = Math.ceil(file.size / chunkSize);

  for (let i = 0; i < chunks; i++) {
    const chunk = file.slice(i * chunkSize, (i + 1) * chunkSize);
    await uploadChunk(chunk, i, chunks);
  }
};
```

### 3. 实时进度更新
```typescript
// WebSocket 连接管理
const useWebSocket = (taskId: string) => {
  useEffect(() => {
    const ws = new WebSocket(`/api/ws/task/${taskId}`);

    ws.onmessage = (event) => {
      const data = JSON.parse(event.data);
      updateTaskProgress(taskId, data.progress);
    };

    return () => ws.close();
  }, [taskId]);
};
```

---

## 🔍 质量保证

### 代码审查清单
- [ ] TypeScript 类型安全
- [ ] 组件 Props 接口定义
- [ ] 错误边界处理
- [ ] 加载状态处理
- [ ] 响应式设计验证
- [ ] 无障碍访问支持
- [ ] 性能优化检查

### 测试策略
- [ ] 单元测试 (Jest + Testing Library)
- [ ] 集成测试 (API 端点)
- [ ] E2E 测试 (Playwright)
- [ ] 视觉回归测试
- [ ] 性能测试 (Lighthouse)

---

## 📈 监控与分析

### 用户行为追踪
```typescript
// 生成事件追踪
const trackGeneration = (mode: string, model: string) => {
  analytics.track('video_generation_started', {
    mode,
    model,
    timestamp: Date.now(),
  });
};
```

### 错误监控
```typescript
// Sentry 错误捕获
import * as Sentry from '@sentry/nextjs';

const handleGenerationError = (error: Error, context: any) => {
  Sentry.captureException(error, {
    tags: { feature: 'video_generation' },
    extra: context,
  });
};
```

---

## 🎉 项目完成标准

### 功能完整性检查
- [ ] 所有生成模式正常工作
- [ ] 参数控制功能完整
- [ ] 预览播放流畅
- [ ] Credits 系统准确
- [ ] 错误处理完善

### 用户体验检查
- [ ] 界面响应速度 < 200ms
- [ ] 移动端体验良好
- [ ] 加载状态清晰
- [ ] 错误提示友好
- [ ] 操作流程顺畅

### 代码质量检查
- [ ] TypeScript 覆盖率 > 95%
- [ ] 组件复用率 > 80%
- [ ] 代码重复率 < 5%
- [ ] 性能评分 > 90
- [ ] 无障碍评分 > 90

🚀 **准备开始开发吧！按照这个 TODOLIST 逐步实现，您将获得一个高质量的 AI 视频生成器复刻版本。**
