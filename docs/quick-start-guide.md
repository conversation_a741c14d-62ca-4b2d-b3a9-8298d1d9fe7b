# 视频生成功能快速开始指南

## 🚀 快速开始

这个指南将帮助你在30分钟内完成基础的视频生成功能集成。

## 📋 前置条件

- [x] 前端UI组件已完成
- [x] 后端Kling SDK已集成
- [x] 数据库和认证系统可用
- [x] Credits系统正常运行

## ⚡ 快速实施步骤

### Step 1: 创建API路由 (10分钟)

```bash
# 创建API文件
mkdir -p src/app/api/video/{generate,status}
```

**创建生成API:**
```typescript
// src/app/api/video/generate/route.ts
import { kling } from "@/aisdk/kling";
import { respData, respErr } from "@/lib/resp";
import { getUserUuid } from "@/services/user";

export async function POST(req: Request) {
  try {
    const { model, prompt, parameters } = await req.json();
    
    const user_uuid = await getUserUuid();
    if (!user_uuid) return respErr("未认证");

    // 简化版：直接调用Kling API
    const videoModel = kling.video(model);
    const result = await videoModel.doGenerate({
      prompt,
      n: 1,
      providerOptions: {
        kling: {
          duration: parameters.duration || 5,
          aspect_ratio: "16:9"
        }
      }
    });

    return respData({
      taskId: `task_${Date.now()}`,
      videos: result.videos,
      status: 'completed'
    });

  } catch (error) {
    return respErr("生成失败");
  }
}
```

**创建状态API:**
```typescript
// src/app/api/video/status/route.ts
import { respData } from "@/lib/resp";

export async function GET(req: Request) {
  const { searchParams } = new URL(req.url);
  const taskIds = searchParams.get('taskIds')?.split(',') || [];
  
  // 简化版：返回模拟状态
  const tasks = taskIds.map(id => ({
    id,
    status: 'completed',
    progress: 100,
    result: {
      videoUrl: 'https://example.com/video.mp4'
    }
  }));

  return respData({ tasks });
}
```

### Step 2: 创建生成Hook (10分钟)

```typescript
// src/hooks/useGenerate.ts
import { useState } from 'react';

interface GenerationParams {
  mode: string;
  model: string;
  prompt: string;
  parameters: any;
}

export function useGenerate() {
  const [isGenerating, setIsGenerating] = useState(false);
  const [result, setResult] = useState<any>(null);

  const generate = async (params: GenerationParams) => {
    setIsGenerating(true);
    try {
      const response = await fetch('/api/video/generate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(params)
      });

      const data = await response.json();
      
      if (data.code === 0) {
        setResult(data.data);
        return data.data.taskId;
      } else {
        throw new Error(data.message);
      }
    } finally {
      setIsGenerating(false);
    }
  };

  return { generate, isGenerating, result };
}
```

### Step 3: 更新ParamPanel组件 (5分钟)

```typescript
// 在 src/components/workspace/param-panel.tsx 中添加
import { useGenerate } from "@/hooks/useGenerate";

// 在组件内部添加
const { generate, isGenerating, result } = useGenerate();

// 更新 handleGenerate 函数
const handleGenerate = async () => {
  try {
    await generate({
      mode,
      model: selectedModel,
      prompt,
      parameters: {
        resolution,
        duration,
        seed,
        generateAudio,
        negativePrompt,
        outputCount,
        publicVisibility,
        copyProtection
      }
    });
    
    alert('视频生成成功！');
  } catch (error) {
    alert('生成失败：' + error.message);
  }
};
```

### Step 4: 更新PreviewPane组件 (5分钟)

```typescript
// 在 src/components/workspace/preview-pane.tsx 中添加
import { useGenerate } from "@/hooks/useGenerate";

export default function PreviewPane() {
  const { result, isGenerating } = useGenerate();

  return (
    <Card className="flex-1 flex flex-col">
      <CardHeader>
        <h3 className="text-lg font-semibold">预览</h3>
      </CardHeader>
      
      <CardContent className="flex-1 flex items-center justify-center">
        {isGenerating ? (
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
            <p>正在生成视频...</p>
          </div>
        ) : result?.videos?.length > 0 ? (
          <div className="text-center">
            <p className="text-green-600 mb-2">✅ 视频生成成功！</p>
            <p className="text-sm text-muted-foreground">
              生成了 {result.videos.length} 个视频
            </p>
          </div>
        ) : (
          <div className="text-center text-muted-foreground">
            <p>请输入提示词并点击生成</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
```

## 🧪 测试验证

### 1. 启动开发服务器
```bash
npm run dev
```

### 2. 测试流程
1. 打开 `http://localhost:3000/txt2vid`
2. 选择模型 `kling-v1`
3. 输入提示词："A cat playing in the garden"
4. 点击生成按钮
5. 查看PreviewPane是否显示生成状态

### 3. API测试
```bash
# 测试生成API
curl -X POST http://localhost:3000/api/video/generate \
  -H "Content-Type: application/json" \
  -d '{
    "model": "kling-v1",
    "prompt": "A cat playing",
    "parameters": {
      "duration": 5,
      "resolution": "720p"
    }
  }'

# 测试状态API
curl "http://localhost:3000/api/video/status?taskIds=task_123"
```

## 🔧 常见问题

### Q: API调用失败怎么办？
**A:** 检查以下几点：
- 环境变量 `KLING_ACCESS_KEY` 和 `KLING_SECRET_KEY` 是否正确设置
- 网络连接是否正常
- Kling API配额是否充足

### Q: 前端组件没有响应？
**A:** 检查：
- 浏览器控制台是否有错误
- Hook是否正确导入和使用
- 组件状态是否正确更新

### Q: 视频生成时间过长？
**A:** 这是正常的，Kling API通常需要30秒到几分钟。后续会添加：
- 轮询机制
- 进度显示
- 后台任务处理

## 🚀 下一步优化

完成基础功能后，可以按以下顺序优化：

1. **添加轮询机制** - 实时更新生成状态
2. **集成数据库** - 持久化任务记录
3. **添加进度条** - 更好的用户体验
4. **错误处理** - 完善的错误提示
5. **文件存储** - 视频结果持久化

## 📚 相关文档

- [完整集成方案](./frontend-backend-integration-plan.md)
- [详细实施清单](./implementation-checklist.md)
- [Kling API文档](../src/aisdk/kling/README.md)

## 💡 提示

- 先确保基础功能可用，再逐步添加复杂特性
- 使用浏览器开发者工具调试API调用
- 关注控制台日志，及时发现问题
- 保持代码简洁，便于后续扩展

---

🎉 **恭喜！** 你已经完成了基础的视频生成功能集成。现在可以开始测试和优化了！
